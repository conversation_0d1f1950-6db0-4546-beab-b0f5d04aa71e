{"mcpServers": {"mcp-feedback-enhanced": {"command": "/Users/<USER>/Desktop/visa-planner-frontend/.venv/bin/uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp"], "cwd": "/Users/<USER>/Desktop/qianzheng<PERSON>hou", "env": {}, "healthCheck": {"period": 30000, "retries": 3}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "memory": {"command": "npx", "args": ["-y", "@itseasy21/mcp-knowledge-graph"], "env": {"MEMORY_FILE_PATH": "./data/memory.jsonl"}}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "cwd": "/Users/<USER>/Desktop/qianzheng<PERSON>hou", "env": {"DATA_DIR": "/Users/<USER>/Desktop/qianzhengzhushou/data/shrimp-task-manager"}, "healthCheck": {"period": 30000, "retries": 3}}, "BroswerTools": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp@latest"], "env": {"BROWSER_TOOLS_HOST": "localhost", "BROWSER_TOOLS_PORT": "3025"}, "healthCheck": {"period": 30000, "retries": 3}}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "ai-collaboration": {"command": "node", "args": ["/Users/<USER>/ai-collaboration-mcp-server.js"], "env": {"OPENROUTER_API_KEY": "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"}, "healthCheck": {"period": 30000, "retries": 3}}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server"]}}}