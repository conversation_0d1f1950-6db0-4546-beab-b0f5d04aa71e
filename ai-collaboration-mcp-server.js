#!/usr/bin/env node

/**
 * AI多核协作MCP服务器
 * 实现Claude Sonnet 4、GPT-4o、Gemini多AI并发协作
 * 兼容Cursor MCP客户端的非标准实现
 */

const https = require('https');

// OpenRouter API配置
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || '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';

// AI模型配置
const AI_MODELS = {
  'claude-architect': {
    model: 'anthropic/claude-sonnet-4',
    role: '系统架构师',
    specialties: ['架构设计', '系统规划', '技术选型', '代码审查']
  },
  'gpt-coder': {
    model: 'openai/gpt-4o', 
    role: '代码实现专家',
    specialties: ['前端开发', 'React组件', 'API接口', '代码优化']
  },
  'gemini-tester': {
    model: 'google/gemini-2.0-flash',
    role: '测试质量专家', 
    specialties: ['单元测试', '集成测试', '质量保证', '性能优化']
  }
};

// MCP协议处理器 - 兼容Cursor客户端
class MCPServer {
  constructor() {
    this.requestId = 0;
    this.protocolVersion = "2024-11-05";
    this.serverInfo = {
      name: 'ai-collaboration-mcp',
      version: '1.0.0'
    };
    this.setupHandlers();
  }

  setupHandlers() {
    // 启动消息输出到stderr（避免干扰JSON-RPC通信）
    console.error('AI协作MCP服务器启动中...');
    
    process.stdin.resume();
    process.stdin.setEncoding('utf8');

    let buffer = '';
    process.stdin.on('data', (chunk) => {
      buffer += chunk;
      let newlineIndex;
      
      while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
        const line = buffer.slice(0, newlineIndex).trim();
        buffer = buffer.slice(newlineIndex + 1);
        
        if (line) {
          try {
            const request = JSON.parse(line);
            this.handleRequest(request);
          } catch (error) {
            // 改进的错误处理 - 只在有id时发送错误响应
            if (line.includes('"id"')) {
              try {
                const partialRequest = JSON.parse(line);
                if (partialRequest.id !== undefined) {
                  this.sendError(partialRequest.id, -32700, 'Parse error', error.message);
                }
              } catch (e) {
                // 无法解析的消息，静默忽略
              }
            }
          }
        }
      }
    });

    process.stdin.on('end', () => {
      process.exit(0);
    });

    process.on('SIGINT', () => {
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      process.exit(0);
    });
  }

  async handleRequest(request) {
    // 添加更严格的请求验证
    if (!request || typeof request !== 'object') {
      return;
    }
    
    const { id, method, params } = request;

    try {
      switch (method) {
        case 'initialize':
          if (id === undefined) {
            return;
          }
          
          // 兼容Cursor客户端：忽略capabilities中的tools字段等非标准实现
          const clientProtocolVersion = params?.protocolVersion;
          if (clientProtocolVersion && clientProtocolVersion !== this.protocolVersion) {
            this.sendError(id, -32602, `不支持的协议版本: ${clientProtocolVersion}. 支持的版本: ${this.protocolVersion}`);
            return;
          }

          // 标准MCP响应格式
          this.sendResponse(id, {
            protocolVersion: this.protocolVersion,
            capabilities: {
              tools: {}  // 空对象表示支持工具但没有特殊能力要求
            },
            serverInfo: this.serverInfo
          });
          break;

        case 'initialized':
          // 初始化完成通知，这是一个notification，不需要响应
          break;

        case 'tools/list':
          if (id === undefined) {
            return;
          }
          this.sendResponse(id, {
            tools: [
              {
                name: 'ai_collaborate',
                description: '启动多AI协作模式，分配任务给不同AI专家',
                inputSchema: {
                  type: 'object',
                  properties: {
                    task: {
                      type: 'string',
                      description: '协作任务描述'
                    },
                    specialists: {
                      type: 'array',
                      items: {
                        type: 'string',
                        enum: ['claude-architect', 'gpt-coder', 'gemini-tester']
                      },
                      description: '参与协作的AI专家'
                    }
                  },
                  required: ['task', 'specialists']
                }
              },
              {
                name: 'ai_call',
                description: '调用特定AI模型处理任务',
                inputSchema: {
                  type: 'object',
                  properties: {
                    aiModel: {
                      type: 'string',
                      enum: ['claude-architect', 'gpt-coder', 'gemini-tester'],
                      description: 'AI专家类型'
                    },
                    prompt: {
                      type: 'string',
                      description: '发送给AI的提示内容'
                    }
                  },
                  required: ['aiModel', 'prompt']
                }
              },
              {
                name: 'collaboration_status',
                description: '查看当前协作状态和AI专家可用性',
                inputSchema: {
                  type: 'object',
                  properties: {},
                  required: []
                }
              }
            ]
          });
          break;

        case 'tools/call':
          if (id === undefined) {
            return;
          }
          await this.handleToolCall(id, params);
          break;

        default:
          if (id !== undefined) {
            this.sendError(id, -32601, `未知方法: ${method}`);
          }
      }
    } catch (error) {
      if (id !== undefined) {
        this.sendError(id, -32603, `内部错误: ${error.message}`);
      }
    }
  }

  async handleToolCall(id, params) {
    const { name, arguments: args } = params;

    try {
      switch (name) {
        case 'ai_collaborate':
          await this.handleCollaboration(id, args);
          break;
        case 'ai_call':
          await this.handleAICall(id, args);
          break;
        case 'collaboration_status':
          await this.handleCollaborationStatus(id);
          break;
        default:
          this.sendError(id, -32602, `未知工具: ${name}`);
      }
    } catch (error) {
      this.sendResponse(id, {
        content: [
          {
            type: 'text',
            text: `错误: ${error.message}`
          }
        ],
        isError: true
      });
    }
  }

  async handleCollaboration(id, args) {
    const { task, specialists } = args;
    
    const sessionId = `collab_${Date.now()}`;
    const results = [];

    // 并行协作模式
    const promises = specialists.map(async (specialist) => {
      const aiConfig = AI_MODELS[specialist];
      const contextualPrompt = `ultrathink 作为${aiConfig.role}，专精于${aiConfig.specialties.join('、')}，请处理以下任务：\n\n${task}`;
      
      try {
        const response = await this.callOpenRouterAPI(aiConfig.model, contextualPrompt);
        return {
          specialist,
          role: aiConfig.role,
          response: response.choices[0].message.content,
          status: 'success'
        };
      } catch (error) {
        return {
          specialist,
          role: aiConfig.role,
          response: `API调用失败: ${error.message}`,
          status: 'error'
        };
      }
    });

    const parallelResults = await Promise.all(promises);
    results.push(...parallelResults);

    // 格式化协作结果
    let formattedOutput = `# 多AI协作结果 - 会话 ${sessionId}\n\n`;
    formattedOutput += `## 📋 任务描述\n${task}\n\n`;
    formattedOutput += `## 🤖 协作模式\n并行协作\n\n`;
    formattedOutput += `## 💡 专家意见\n\n`;

    results.forEach((result, index) => {
      formattedOutput += `### ${index + 1}. ${result.role} (${result.specialist})\n`;
      formattedOutput += `**状态**: ${result.status === 'success' ? '✅ 完成' : '❌ 失败'}\n\n`;
      formattedOutput += `**回复**:\n${result.response}\n\n`;
      formattedOutput += `---\n\n`;
    });

    this.sendResponse(id, {
      content: [
        {
          type: 'text',
          text: formattedOutput
        }
      ],
      isError: false
    });
  }

  async handleAICall(id, args) {
    const { aiModel, prompt } = args;
    
    const aiConfig = AI_MODELS[aiModel];
    if (!aiConfig) {
      throw new Error(`未知AI模型: ${aiModel}`);
    }
    
    try {
      const enhancedPrompt = `ultrathink ${prompt}`;
      const response = await this.callOpenRouterAPI(aiConfig.model, enhancedPrompt);
      
      this.sendResponse(id, {
        content: [
          {
            type: 'text',
            text: `## ${aiConfig.role}回复 (${aiModel})\n\n${response.choices[0].message.content}`
          }
        ],
        isError: false
      });
    } catch (error) {
      throw new Error(`AI调用失败: ${error.message}`);
    }
  }

  async handleCollaborationStatus(id) {
    this.sendResponse(id, {
      content: [
        {
          type: 'text', 
          text: `## 🎯 AI协作系统状态\n\n**可用AI专家**:\n- Claude Sonnet 4 架构师 ✅\n- GPT-4o 编码专家 ✅\n- Gemini 测试专家 ✅\n\n**API状态**: 正常连接\n**协作模式**: 就绪\n\n**使用方法**:\n- \`ai_collaborate\`: 启动多AI协作\n- \`ai_call\`: 调用单个AI专家\n- \`collaboration_status\`: 查看系统状态`
        }
      ],
      isError: false
    });
  }

  async callOpenRouterAPI(model, prompt) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify({
        model: model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000
      });

      const options = {
        hostname: 'openrouter.ai',
        port: 443,
        path: '/api/v1/chat/completions',
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData),
          'HTTP-Referer': 'https://cursor.sh',
          'X-Title': 'AI Collaboration MCP Server'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (res.statusCode === 200) {
              resolve(response);
            } else {
              reject(new Error(`API错误 ${res.statusCode}: ${response.error?.message || data}`));
            }
          } catch (error) {
            reject(new Error(`JSON解析错误: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(new Error(`请求错误: ${error.message}`));
      });

      req.write(postData);
      req.end();
    });
  }

  sendResponse(id, result) {
    const response = {
      jsonrpc: '2.0',
      id: id,
      result: result
    };
    console.log(JSON.stringify(response));
  }

  sendError(id, code, message, data = null) {
    const response = {
      jsonrpc: '2.0',
      id: id,
      error: {
        code: code,
        message: message,
        data: data
      }
    };
    console.log(JSON.stringify(response));
  }
}

// 启动服务器
if (require.main === module) {
  const server = new MCPServer();
}

module.exports = MCPServer; 