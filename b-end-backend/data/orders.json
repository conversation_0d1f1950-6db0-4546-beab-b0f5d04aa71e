[{"id": "o001", "orderId": "o001", "customerName": "王小明", "customerPhone": "***********", "customerEmail": "<EMAIL>", "productName": "日本旅游签证", "amount": 299, "paymentStatus": "paid", "status": "processing", "notes": "客户要求加急处理", "productId": "jp-tourist-visa", "merchantId": "m001", "createdAt": "2025-06-20T09:30:00Z", "updatedAt": "2025-06-20T09:35:00Z", "assignedAt": "2025-06-20T09:30:00Z", "receivedAt": "2025-06-20T09:30:00Z"}, {"id": "o002", "orderId": "o002", "customerName": "李小红", "customerPhone": "***********", "customerEmail": "<EMAIL>", "productName": "韩国旅游签证", "amount": 199, "paymentStatus": "paid", "status": "completed", "notes": "已完成签证，等待客户取件", "productId": "kr-tourist-visa", "merchantId": "m001", "createdAt": "2025-06-15T14:20:00Z", "updatedAt": "2025-06-18T16:45:00Z", "assignedAt": "2025-06-15T14:20:00Z", "receivedAt": "2025-06-15T14:20:00Z"}, {"id": "o003", "orderId": "o003", "customerName": "张三", "customerPhone": "13666666666", "customerEmail": "<EMAIL>", "productName": "美国旅游签证", "amount": 599, "paymentStatus": "pending", "status": "pending", "notes": "等待客户上传材料", "productId": "us-tourist-visa", "merchantId": "m002", "createdAt": "2025-06-25T11:15:00Z", "updatedAt": "2025-06-25T11:15:00Z", "assignedAt": "2025-06-25T11:15:00Z", "receivedAt": "2025-06-25T11:15:00Z"}, {"id": "o004", "orderId": "o004", "customerName": "李四", "customerPhone": "13555555555", "customerEmail": "<EMAIL>", "productName": "澳大利亚旅游签证", "amount": 499, "paymentStatus": "refunded", "status": "rejected", "notes": "签证被拒，已全额退款", "productId": "aus-tourist-visa", "merchantId": "m002", "createdAt": "2025-06-10T08:45:00Z", "updatedAt": "2025-06-17T14:30:00Z", "assignedAt": "2025-06-10T08:45:00Z", "receivedAt": "2025-06-10T08:45:00Z"}, {"id": "o005", "orderId": "o005", "customerName": "赵六", "customerPhone": "13444444444", "customerEmail": "z<PERSON><PERSON><PERSON>@example.com", "productName": "日本旅游签证", "amount": 299, "paymentStatus": "paid", "status": "processing", "notes": "材料已提交使馆，等待审核", "productId": "jp-tourist-visa", "merchantId": "m001", "createdAt": "2025-06-23T16:20:00Z", "updatedAt": "2025-06-24T09:15:00Z", "assignedAt": "2025-06-23T16:20:00Z", "receivedAt": "2025-06-23T16:20:00Z"}, {"id": "o1751983809019", "orderId": "o1751983809019", "customerName": "测试用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "productName": "日本旅游签证代办", "amount": 399, "paymentStatus": "pending", "status": "pending", "notes": "通过AI咨询生成的定制签证方案", "productId": "custom-visa-service", "merchantId": "m001", "createdAt": "2025-07-08T14:10:09.059Z", "updatedAt": "2025-07-08T14:10:09.059Z", "assignedAt": "2025-07-08T14:10:09.059Z", "receivedAt": "2025-07-08T14:10:09.059Z"}, {"orderId": "v20250711004", "customerName": "张三", "customerEmail": "<EMAIL>", "status": "completed", "assignedAt": "2025-07-11T10:19:25.043Z", "merchantId": "m001", "receivedAt": "2025-07-11T10:19:25.134Z", "updatedAt": "2025-07-12T05:15:36.509Z", "id": "v20250711004", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "amount": 1299, "notes": "", "createdAt": "2025-07-11T10:19:25.043Z", "businessType": "visa", "productId": "visa_product_001"}, {"orderId": "v20250711005", "customerName": "李四", "customerEmail": "<EMAIL>", "productName": "日本旅游签证", "status": "processing", "amount": 899, "assignedAt": "2025-07-11T10:19:52.683Z", "merchantId": "m001", "receivedAt": "2025-07-11T10:19:52.686Z", "updatedAt": "2025-07-12T05:15:39.954Z", "id": "v20250711005", "paymentStatus": "pending", "customerPhone": "***********", "notes": "通过AI咨询生成的订单", "createdAt": "2025-07-11T10:19:52.683Z", "businessType": "visa", "productId": "custom-visa-service"}, {"orderId": "h20250711003", "customerName": "王五", "customerEmail": "<EMAIL>", "productName": "东京酒店预订", "status": "pending", "amount": 1200, "assignedAt": "2025-07-11T10:20:04.350Z", "merchantId": "m001", "receivedAt": "2025-07-11T10:20:04.353Z", "id": "h20250711003", "paymentStatus": "pending", "customerPhone": "***********", "notes": "通过AI咨询生成的订单", "createdAt": "2025-07-11T10:20:04.350Z", "updatedAt": "2025-07-11T10:20:04.350Z", "businessType": "hotel", "productId": "custom-visa-service"}, {"orderId": "v20250711006", "customerName": "测试用户", "customerEmail": "<EMAIL>", "status": "pending", "assignedAt": "2025-07-11T10:25:01.706Z", "merchantId": "m001", "receivedAt": "2025-07-11T10:25:01.709Z", "id": "v20250711006", "paymentStatus": "paid", "customerPhone": "***********", "productName": "未知产品", "amount": 999, "notes": "", "createdAt": "2025-07-11T10:25:01.706Z", "updatedAt": "2025-07-11T10:25:01.706Z", "businessType": "visa", "productId": "test_product_001"}, {"orderId": "h20250711004", "customerName": "测试用户2", "customerEmail": "<EMAIL>", "status": "pending", "assignedAt": "2025-07-11T10:25:34.373Z", "merchantId": "m002", "receivedAt": "2025-07-11T10:25:34.376Z", "id": "h20250711004", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "amount": 1599, "notes": "", "createdAt": "2025-07-11T10:25:34.373Z", "updatedAt": "2025-07-11T10:25:34.373Z", "businessType": "hotel", "productId": "test_product_002"}, {"orderId": "a20250711001", "customerName": "测试用户3", "customerEmail": "<EMAIL>", "status": "pending", "assignedAt": "2025-07-11T10:25:53.385Z", "merchantId": "m003", "receivedAt": "2025-07-11T10:25:53.387Z", "id": "a20250711001", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "amount": 299, "notes": "", "createdAt": "2025-07-11T10:25:53.385Z", "updatedAt": "2025-07-11T10:25:53.385Z", "businessType": "attraction", "productId": "test_product_003"}, {"orderId": "f20250711001", "customerName": "最终测试用户", "customerEmail": "<EMAIL>", "status": "pending", "assignedAt": "2025-07-11T10:51:09.357Z", "merchantId": "m004", "receivedAt": "2025-07-11T10:51:09.360Z", "id": "f20250711001", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "amount": 2999, "notes": "", "createdAt": "2025-07-11T10:51:09.357Z", "updatedAt": "2025-07-11T10:51:09.357Z", "businessType": "flight", "productId": "final_test_001"}, {"orderId": "v20250711007", "customerName": "调试用户", "customerEmail": "<EMAIL>", "status": "pending", "amount": 888, "assignedAt": "2025-07-11T10:51:28.099Z", "merchantId": "m999", "receivedAt": "2025-07-11T10:51:28.101Z", "id": "v20250711007", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "notes": "", "createdAt": "2025-07-11T10:51:28.099Z", "updatedAt": "2025-07-11T10:51:28.099Z", "businessType": "visa", "productId": "debug_test_001"}, {"orderId": "t20250711003", "customerName": "价格测试用户", "customerEmail": "<EMAIL>", "status": "pending", "assignedAt": "2025-07-11T10:51:46.141Z", "merchantId": "m888", "receivedAt": "2025-07-11T10:51:46.144Z", "id": "t20250711003", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "amount": 1234, "notes": "", "createdAt": "2025-07-11T10:51:46.141Z", "updatedAt": "2025-07-11T10:51:46.141Z", "businessType": "travel_line", "productId": "price_test_001"}, {"id": "c20250711001", "orderId": "c20250711001", "customerName": "最终调试用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "productName": "未知产品", "amount": 777, "paymentStatus": "pending", "status": "pending", "notes": "", "businessType": "car_rental", "productId": "debug_final_test", "merchantId": "m777", "createdAt": "2025-07-11T10:52:43.619Z", "updatedAt": "2025-07-11T10:52:43.619Z", "assignedAt": "2025-07-11T10:52:43.619Z", "receivedAt": "2025-07-11T10:52:43.619Z"}, {"orderId": "v20250711008", "customerName": "testuser", "customerEmail": "<EMAIL>", "status": "pending", "amount": 1000, "assignedAt": "2025-07-11T11:33:17.063Z", "merchantId": "m001", "receivedAt": "2025-07-11T11:33:17.081Z", "id": "v20250711008", "paymentStatus": "pending", "customerPhone": "***********", "productName": "未知产品", "notes": "", "createdAt": "2025-07-11T11:33:17.063Z", "updatedAt": "2025-07-11T11:33:17.063Z", "businessType": "visa", "productId": "prod001"}, {"orderId": "h20250711005", "customerName": "测试创建用户", "customerEmail": "<EMAIL>", "productName": "测试创建产品", "status": "pending", "amount": 666, "assignedAt": "2025-07-11T11:47:34.684Z", "merchantId": "m002", "receivedAt": "2025-07-11T11:47:34.687Z", "id": "h20250711005", "paymentStatus": "pending", "customerPhone": "***********", "notes": "通过前台创建页面测试", "createdAt": "2025-07-11T11:47:34.684Z", "updatedAt": "2025-07-11T11:47:34.684Z", "businessType": "hotel", "productId": "test_create_001"}, {"orderId": "v20250711009", "customerName": "song lei", "customerEmail": "<EMAIL>", "productName": "日本个人旅游签证", "status": "pending", "amount": 999, "assignedAt": "2025-07-11T11:49:15.271Z", "merchantId": "m001", "receivedAt": "2025-07-11T11:49:15.273Z", "id": "v20250711009", "paymentStatus": "pending", "customerPhone": "***********", "notes": "测试订单", "createdAt": "2025-07-11T11:49:15.271Z", "updatedAt": "2025-07-11T11:49:15.271Z", "businessType": "visa", "productId": "v20250711001"}, {"orderId": "v20250711010", "customerName": "song lei", "customerEmail": "<EMAIL>", "productName": "日本个人旅游签证", "status": "pending", "amount": 999, "assignedAt": "2025-07-11T11:53:21.608Z", "merchantId": "m001", "receivedAt": "2025-07-11T11:53:21.611Z", "id": "v20250711010", "paymentStatus": "pending", "customerPhone": "***********", "notes": "", "createdAt": "2025-07-11T11:53:21.608Z", "updatedAt": "2025-07-11T11:53:21.608Z", "businessType": "visa", "productId": "v20250711001"}, {"orderId": "v20250711011", "customerName": "测试产品库用户", "customerEmail": "<EMAIL>", "productName": "日本单次旅游签证", "status": "pending", "amount": 299, "assignedAt": "2025-07-11T12:13:59.729Z", "merchantId": "m001", "receivedAt": "2025-07-11T12:13:59.732Z", "id": "v20250711011", "paymentStatus": "pending", "customerPhone": "***********", "notes": "通过新的产品库创建", "createdAt": "2025-07-11T12:13:59.729Z", "updatedAt": "2025-07-11T12:13:59.729Z", "businessType": "visa", "productId": "vp_68b4aef5-2da6-47a1-abd8-ea7699dd8652"}, {"orderId": "v20250711012", "customerName": "song lei", "customerEmail": "<EMAIL>", "status": "pending", "amount": 299, "assignedAt": "2025-07-11T12:59:23.699Z", "merchantId": "m001", "receivedAt": "2025-07-11T12:59:23.703Z", "id": "v20250711012", "paymentStatus": "paid", "customerPhone": "***********", "productName": "未知产品", "notes": "测试", "createdAt": "2025-07-11T12:59:23.699Z", "updatedAt": "2025-07-11T12:59:23.699Z", "businessType": "visa", "productId": "vp_68b4aef5-2da6-47a1-abd8-ea7699dd8652"}, {"orderId": "v20250711013", "customerName": "测试数据同步用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "businessType": "visa", "productId": "vp_68b4aef5-2da6-47a1-abd8-ea7699dd8652", "productName": "日本单次旅游签证", "status": "completed", "paymentStatus": "paid", "amount": 299, "notes": "测试完整数据同步和支付状态", "assignedAt": "2025-07-11T13:38:16.686Z", "merchantId": "m001", "receivedAt": "2025-07-11T13:38:16.784Z", "updatedAt": "2025-07-12T05:04:08.963Z", "id": "v20250711013", "createdAt": "2025-07-11T13:38:16.686Z"}, {"orderId": "v20250712001", "customerName": "重启测试用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "businessType": "visa", "productId": "vp_68b4aef5-2da6-47a1-abd8-ea7699dd8652", "productName": "日本单次旅游签证", "status": "pending", "paymentStatus": "pending", "amount": 299, "notes": "测试重启后的创建功能", "assignedAt": "2025-07-12T05:04:16.387Z", "merchantId": "m001", "receivedAt": "2025-07-12T05:04:16.404Z", "id": "v20250712001", "createdAt": "2025-07-12T05:04:16.387Z", "updatedAt": "2025-07-12T05:04:16.387Z"}, {"id": "ORD-001", "customerName": "张三", "amount": 1250, "status": "已完成", "date": "2025-07-10", "paymentStatus": "pending", "customerPhone": "未提供", "productName": "未知产品", "notes": "", "updatedAt": "2025-07-12T05:49:25.235Z"}, {"id": "ORD-002", "customerName": "李四", "amount": 899.5, "status": "处理中", "date": "2025-07-11", "paymentStatus": "pending", "customerPhone": "未提供", "productName": "未知产品", "notes": "", "updatedAt": "2025-07-12T05:49:25.236Z"}, {"id": "ORD-003", "customerName": "王五", "amount": 2300, "status": "待支付", "date": "2025-07-11", "paymentStatus": "pending", "customerPhone": "未提供", "productName": "未知产品", "notes": "", "updatedAt": "2025-07-12T05:49:25.236Z"}, {"id": "ORD-004", "customerName": "赵六", "amount": 450, "status": "已取消", "date": "2025-07-09", "paymentStatus": "pending", "customerPhone": "未提供", "productName": "未知产品", "notes": "", "updatedAt": "2025-07-12T05:49:25.236Z"}, {"orderId": "v20250713001", "customerName": "测试联调用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "businessType": "visa", "productId": "jp-tourist-visa", "productName": "日本旅游签证", "status": "processing", "paymentStatus": "pending", "amount": 299, "notes": "三端联调穿透测试订单", "assignedAt": "2025-07-13T10:25:21.872Z", "merchantId": "m001", "receivedAt": "2025-07-13T10:25:21.968Z", "updatedAt": "2025-07-13T10:32:37.512Z", "syncSource": "test"}, {"orderId": "v20250713002", "customerName": "系统测试用户", "customerPhone": "***********", "customerEmail": "<EMAIL>", "businessType": "visa", "productId": "test-visa", "productName": "系统测试签证", "status": "pending", "paymentStatus": "pending", "amount": 199, "notes": "系统完整性测试订单", "assignedAt": "2025-07-13T12:09:34.642Z", "merchantId": "m001", "receivedAt": "2025-07-13T12:09:34.645Z"}]